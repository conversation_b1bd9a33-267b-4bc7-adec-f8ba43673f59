package executors

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"workflow-engine/internal/domain/workflow"
)

// Document represents a knowledge base document
type Document struct {
	ID       string                 `json:"id"`
	Content  string                 `json:"content"`
	Metadata map[string]interface{} `json:"metadata"`
	Vector   []float64              `json:"vector,omitempty"`
}

// KnowledgeBase represents a knowledge base
type KnowledgeBase struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Documents []Document `json:"documents"`
}

// KnowledgeExecutor handles knowledge base operations
type KnowledgeExecutor struct {
	knowledgeBases map[string]*KnowledgeBase
	embeddings     *EmbeddingService
}

// EmbeddingService provides text embedding functionality
type EmbeddingService struct {
	// Placeholder for embedding service (could integrate with OpenAI, Cohere, etc.)
}

// NewKnowledgeExecutor creates a new knowledge executor
func NewKnowledgeExecutor() *KnowledgeExecutor {
	return &KnowledgeExecutor{
		knowledgeBases: make(map[string]*KnowledgeBase),
		embeddings:     &EmbeddingService{},
	}
}

// ExecuteKnowledgeRetriever executes a knowledge retriever node
func (e *KnowledgeExecutor) ExecuteKnowledgeRetriever(ctx context.Context, node *workflow.NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Type assert config to map[string]interface{}
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config type, expected map[string]interface{}")
	}

	// Get knowledge base ID
	kbID, ok := config["knowledge_base"].(string)
	if !ok {
		return nil, fmt.Errorf("knowledge_base is required")
	}

	// Get query
	query, ok := input["query"].(string)
	if !ok {
		return nil, fmt.Errorf("query is required")
	}

	// Get knowledge base
	kb, exists := e.knowledgeBases[kbID]
	if !exists {
		return nil, fmt.Errorf("knowledge base not found: %s", kbID)
	}

	// Get configuration parameters
	maxResults := 10
	if max, ok := config["max_results"].(float64); ok {
		maxResults = int(max)
	}

	minRelevance := 0.7
	if min, ok := config["min_relevance"].(float64); ok {
		minRelevance = min
	}

	includeMetadata := true
	if include, ok := config["include_metadata"].(bool); ok {
		includeMetadata = include
	}

	// Get filters if provided
	var filters map[string]interface{}
	if f, ok := input["filters"].(map[string]interface{}); ok {
		filters = f
	}

	// Perform search
	results, scores, err := e.searchDocuments(ctx, kb, query, maxResults, minRelevance, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to search documents: %w", err)
	}

	// Format results
	var formattedResults []map[string]interface{}
	for i, doc := range results {
		result := map[string]interface{}{
			"id":      doc.ID,
			"content": doc.Content,
			"score":   scores[i],
		}

		if includeMetadata {
			result["metadata"] = doc.Metadata
		}

		formattedResults = append(formattedResults, result)
	}

	return map[string]interface{}{
		"results":          formattedResults,
		"relevance_scores": scores,
		"total_count":      len(formattedResults),
	}, nil
}

// ExecuteKnowledgeWriter executes a knowledge writer node
func (e *KnowledgeExecutor) ExecuteKnowledgeWriter(ctx context.Context, node *workflow.NodeSchema, input map[string]interface{}) (map[string]interface{}, error) {
	// Type assert config to map[string]interface{}
	config, ok := node.Config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config type, expected map[string]interface{}")
	}

	// Get knowledge base ID
	kbID, ok := config["knowledge_base"].(string)
	if !ok {
		return nil, fmt.Errorf("knowledge_base is required")
	}

	// Get content
	content, ok := input["content"].(string)
	if !ok {
		return nil, fmt.Errorf("content is required")
	}

	// Get metadata
	var metadata map[string]interface{}
	if m, ok := input["metadata"].(map[string]interface{}); ok {
		metadata = m
	} else {
		metadata = make(map[string]interface{})
	}

	// Get or create knowledge base
	kb, exists := e.knowledgeBases[kbID]
	if !exists {
		kb = &KnowledgeBase{
			ID:        kbID,
			Name:      kbID,
			Documents: []Document{},
		}
		e.knowledgeBases[kbID] = kb
	}

	// Get configuration parameters
	chunkSize := 1000
	if size, ok := config["chunk_size"].(float64); ok {
		chunkSize = int(size)
	}

	overlap := 100
	if o, ok := config["overlap"].(float64); ok {
		overlap = int(o)
	}

	// Split content into chunks
	chunks := e.splitTextIntoChunks(content, chunkSize, overlap)

	// Add documents to knowledge base
	var documentIDs []string
	for i, chunk := range chunks {
		docID := fmt.Sprintf("%s_%d_%d", kbID, time.Now().Unix(), i)

		// Add chunk metadata
		chunkMetadata := make(map[string]interface{})
		for k, v := range metadata {
			chunkMetadata[k] = v
		}
		chunkMetadata["chunk_index"] = i
		chunkMetadata["total_chunks"] = len(chunks)
		chunkMetadata["created_at"] = time.Now().Format(time.RFC3339)

		doc := Document{
			ID:       docID,
			Content:  chunk,
			Metadata: chunkMetadata,
		}

		// Generate embedding (placeholder - would use actual embedding service)
		doc.Vector = e.generateEmbedding(chunk)

		kb.Documents = append(kb.Documents, doc)
		documentIDs = append(documentIDs, docID)
	}

	return map[string]interface{}{
		"document_id": strings.Join(documentIDs, ","),
		"success":     true,
	}, nil
}

// searchDocuments searches for documents in a knowledge base
func (e *KnowledgeExecutor) searchDocuments(ctx context.Context, kb *KnowledgeBase, query string, maxResults int, minRelevance float64, filters map[string]interface{}) ([]Document, []float64, error) {
	// Generate query embedding
	queryVector := e.generateEmbedding(query)

	// Calculate similarities
	type docScore struct {
		doc   Document
		score float64
	}

	var candidates []docScore

	for _, doc := range kb.Documents {
		// Apply filters if provided
		if filters != nil && !e.matchesFilters(doc, filters) {
			continue
		}

		// Calculate similarity
		similarity := e.cosineSimilarity(queryVector, doc.Vector)

		if similarity >= minRelevance {
			candidates = append(candidates, docScore{
				doc:   doc,
				score: similarity,
			})
		}
	}

	// Sort by score (descending)
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].score > candidates[j].score
	})

	// Limit results
	if len(candidates) > maxResults {
		candidates = candidates[:maxResults]
	}

	// Extract documents and scores
	var docs []Document
	var scores []float64

	for _, candidate := range candidates {
		docs = append(docs, candidate.doc)
		scores = append(scores, candidate.score)
	}

	return docs, scores, nil
}

// matchesFilters checks if a document matches the given filters
func (e *KnowledgeExecutor) matchesFilters(doc Document, filters map[string]interface{}) bool {
	for key, expectedValue := range filters {
		if actualValue, exists := doc.Metadata[key]; !exists || actualValue != expectedValue {
			return false
		}
	}
	return true
}

// splitTextIntoChunks splits text into chunks with overlap
func (e *KnowledgeExecutor) splitTextIntoChunks(text string, chunkSize, overlap int) []string {
	if len(text) <= chunkSize {
		return []string{text}
	}

	var chunks []string
	start := 0

	for start < len(text) {
		end := start + chunkSize
		if end > len(text) {
			end = len(text)
		}

		chunk := text[start:end]
		chunks = append(chunks, chunk)

		if end == len(text) {
			break
		}

		start = end - overlap
		if start < 0 {
			start = 0
		}
	}

	return chunks
}

// generateEmbedding generates a simple embedding for text (placeholder implementation)
func (e *KnowledgeExecutor) generateEmbedding(text string) []float64 {
	// This is a very simple placeholder implementation
	// In a real system, you would use a proper embedding model
	words := strings.Fields(strings.ToLower(text))
	embedding := make([]float64, 384) // Common embedding dimension

	for i, word := range words {
		if i >= len(embedding) {
			break
		}
		// Simple hash-based embedding (not suitable for production)
		hash := 0
		for _, char := range word {
			hash = hash*31 + int(char)
		}
		embedding[i%len(embedding)] += float64(hash%1000) / 1000.0
	}

	// Normalize
	norm := 0.0
	for _, val := range embedding {
		norm += val * val
	}
	norm = math.Sqrt(norm)

	if norm > 0 {
		for i := range embedding {
			embedding[i] /= norm
		}
	}

	return embedding
}

// cosineSimilarity calculates cosine similarity between two vectors
func (e *KnowledgeExecutor) cosineSimilarity(a, b []float64) float64 {
	if len(a) != len(b) {
		return 0.0
	}

	var dotProduct, normA, normB float64

	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(normA) * math.Sqrt(normB))
}

// GetKnowledgeBase returns a knowledge base by ID
func (e *KnowledgeExecutor) GetKnowledgeBase(id string) (*KnowledgeBase, bool) {
	kb, exists := e.knowledgeBases[id]
	return kb, exists
}

// CreateKnowledgeBase creates a new knowledge base
func (e *KnowledgeExecutor) CreateKnowledgeBase(id, name string) *KnowledgeBase {
	kb := &KnowledgeBase{
		ID:        id,
		Name:      name,
		Documents: []Document{},
	}
	e.knowledgeBases[id] = kb
	return kb
}
